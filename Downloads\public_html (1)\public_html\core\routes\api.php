<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::namespace("Api")->group(function () {
    Route::controller('AppController')->group(function () {
        Route::any('general-setting', 'generalSetting');
        Route::get('get-countries', 'getCountries');
        Route::get('language/{key}', 'getLanguage');
        Route::get('policies', 'policies');
        Route::get('faq', 'faq');
        Route::get('zones', 'zone');
    });

    // Public tracking endpoint (no auth required)
    Route::post('track/{code}', 'TrackingController@trackRideByCode');
});

Route::namespace('Api\User')->group(function () {

    Route::namespace('Auth')->group(function () {
        Route::controller('LoginController')->group(function () {
            Route::post('login', 'login');
            Route::post('check-token', 'checkToken');
            Route::post('social-login', 'socialLogin');
        });
        Route::post('register', 'RegisterController@register');
        Route::controller('ForgotPasswordController')->group(function () {
            Route::post('password/email', 'sendResetCodeEmail');
            Route::post('password/verify-code', 'verifyCode');
            Route::post('password/reset', 'reset');
        });
    });

    Route::middleware(['auth:sanctum', 'token.permission:auth_token'])->group(function () {

        Route::post('user-data-submit', 'UserController@userDataSubmit');

        //authorization
        Route::middleware('registration.complete')->controller('AuthorizationController')->group(function () {
            Route::get('authorization', 'authorization');
            Route::get('resend-verify/{type}', 'sendVerifyCode');
            Route::post('verify-email', 'emailVerification');
            Route::post('verify-mobile', 'mobileVerification');
        });

        Route::middleware(['registration.complete', 'check.status'])->group(function () {

            Route::controller('UserController')->group(function () {

                Route::get('dashboard', 'dashboard');
                Route::post('profile-setting', 'submitProfile');
                Route::post('change-password', 'submitPassword');

                Route::get('user-info', 'userInfo');
                Route::get('balance', 'getBalance');

                //Report

                Route::any('payment/history', 'paymentHistory');

                Route::post('save-device-token', 'addDeviceToken');
                Route::get('push-notifications', 'pushNotifications');
                Route::post('push-notifications/read/{id}', 'pushNotificationsRead');


                Route::post('delete-account', 'deleteAccount');

                Route::post('pusher/auth/{socketId}/{channelName}', 'pusher');
            });

            Route::prefix('ride')->controller('RideController')->group(function () {
                Route::post('fare-and-distance', 'findFareAndDistance');
                Route::post('create', 'create');
                Route::get('bids/{id}', 'bids');
                Route::post('reject/{id}', 'reject');
                Route::post('accept/{bidId}', 'accept');
                Route::get('list', 'list');
                Route::get('scheduled', 'scheduledRides');
                Route::post('cancel/{id}', 'cancel');
                Route::post('sos/{id}', 'sos');
                Route::get('details/{id}', 'details');
                Route::get('payment/{id}', 'payment');
                Route::post('payment/{id}', 'paymentSave');
            });

            // Ride Tracking
            Route::controller('RideTrackingController')->prefix('tracking')->group(function () {
                Route::post('generate-code', 'generateTrackingCode');
                Route::post('track-ride', 'trackRide');
                Route::post('get-location', 'getRideLocation');
            });

            // Chat routes
            Route::prefix('chat')->controller('SharedRideChatController')->group(function () {
                Route::post('send/{sharedRideId}', 'sendMessage');
                Route::get('messages/{sharedRideId}', 'getMessages');
                Route::post('mark-read/{messageId}', 'markAsRead');
                Route::get('unread-count', 'getUnreadCount');
            });

            // Coupon
            Route::controller('CouponController')->group(function () {
                Route::get('coupons', 'coupons');
                Route::post('apply-coupon/{id}', 'applyCoupon');
                Route::post('remove-coupon/{id}', 'removeCoupon');
            });

            Route::controller('ReviewController')->group(function () {
                Route::get('review', 'review');
                Route::post('review/{id}', 'reviewStore');
                Route::get('get-driver-review/{driverId}', 'driverReview');
            });

            // Payment/Wallet
            Route::controller('PaymentController')->group(function () {
                Route::get('deposit/methods', 'methods');
                Route::post('deposit/insert', 'depositInsert');
                Route::get('deposit/history', 'depositHistory');
            });

            Route::controller('TicketController')->prefix('ticket')->group(function () {
                Route::get('/', 'supportTicket');
                Route::post('create', 'storeSupportTicket');
                Route::get('view/{ticket}', 'viewTicket');
                Route::post('reply/{id}', 'replyTicket');
                Route::post('close/{id}', 'closeTicket');
                Route::get('download/{attachment_id}', 'ticketDownload');
            });
            //message
            Route::controller('MessageController')->prefix('ride')->group(function () {
                Route::get('messages/{id}', 'messages');
                Route::post('send/message/{id}', 'messageSave');
            });

            //driver videos
            Route::controller('DriverVideoController')->prefix('ride')->group(function () {
                Route::get('videos/{id}', 'getVideos');
                Route::get('video/{id}', 'getVideo');
                Route::get('unviewed-videos/{id}', 'getUnviewedCount');
            });

            //ride sharing
            Route::controller('RideShareController')->prefix('ride-share')->group(function () {
                Route::post('search-nearby', 'searchNearbyUsers');
                Route::post('send-request', 'sendRequest');
                Route::get('requests', 'getRequests');
                Route::post('accept-request', 'acceptRequest');
                Route::post('reject-request', 'rejectRequest');
                Route::get('details/{id}', 'getSharedRideDetails');
                Route::get('active', 'getActiveSharedRides');
            });

        });
        Route::get('logout', 'Auth\LoginController@logout');
    });
});

//start driver route
Route::namespace('Api\Driver')->prefix('driver')->group(function () {
    Route::namespace('Auth')->group(function () {
        Route::controller('LoginController')->group(function () {
            Route::post('login', 'login');
            Route::post('social-login', 'socialLogin');
        });
        Route::post('register', 'RegisterController@register');

        Route::controller('ForgotPasswordController')->group(function () {
            Route::post('password/email', 'sendResetCodeEmail');
            Route::post('password/verify-code', 'verifyCode');
            Route::post('password/reset', 'reset');
        });
    });

    Route::middleware(['auth:sanctum', 'token.permission:driver_token'])->group(function () {
        //authorization
        Route::post('driver-data-submit', 'DriverController@driverDataSubmit');
        Route::middleware('registration.complete')->group(function () {
            Route::controller('AuthorizationController')->group(function () {
                Route::get('authorization', 'authorization');
                Route::get('resend-verify/{type}', 'sendVerifyCode');
                Route::post('verify-email', 'emailVerification');
                Route::post('verify-mobile', 'mobileVerification');
                Route::post('verify-g2fa', 'g2faVerification');
            });

            Route::middleware(['check.status'])->group(function () {

                Route::controller('DriverController')->group(function () {
                    Route::get('dashboard', 'dashboard');
                    Route::get('driver-info', 'driverInfo');

                    Route::post('profile-setting', 'submitProfile');
                    Route::post('change-password', 'submitPassword');
                    Route::post('delete-account', 'accountDelete');

                    Route::post('pusher/auth/{socketId}/{channelName}', 'pusher');
                    //Driver Verification
                    Route::get('driver-verification', 'driverVerification');
                    Route::post('driver-verification', 'driverVerificationStore');

                    //vehicle verification
                    Route::get('vehicle-verification', 'vehicleVerification');
                    Route::post('vehicle-verification', 'vehicleVerificationStore');

                    //Report
                    Route::any('deposit/history', 'depositHistory');
                    Route::get('transactions', 'transactions');
                    Route::get('payment/history', 'paymentHistory');
                    Route::post('online-status', 'onlineStatus');

                    Route::post('save-device-token', 'addDeviceToken');

                    //2FA
                    Route::get('twofactor', 'show2faForm');
                    Route::post('twofactor/enable', 'create2fa');
                    Route::post('twofactor/disable', 'disable2fa');
                });

                // Driver Location
                Route::controller('LocationController')->prefix('location')->group(function () {
                    Route::post('update', 'updateLocation');
                    Route::get('active-tracked-rides', 'getActiveTrackedRides');
                });

                Route::controller('ReviewController')->group(function () {
                    Route::get('review', 'review');
                    Route::post('review/{rideId}', 'reviewStore');
                    Route::get('get-rider-review/{riderId}', 'riderReview');
                });

                //Withdraw
                Route::middleware('driver.verification')->group(function () {
                    Route::controller('WithdrawController')->group(function () {
                        Route::get('withdraw-method', 'withdrawMethod');
                        Route::post('withdraw-request', 'withdrawStore');
                        Route::post('withdraw-request/confirm', 'withdrawSubmit');
                        Route::get('withdraw/history', 'withdrawLog');
                    });
                    // Rides
                    Route::controller('RideController')->prefix('rides')->group(function () {
                        Route::get('/', 'rides');
                        Route::get('details/{id}', 'details');
                        Route::post('start/{id}', 'start');
                        Route::post('end/{id}', 'end');
                        Route::get('list', 'list');
                        Route::post('received-cash-payment/{id}', 'receivedCashPayment');
                        Route::post('live-location/{id}', 'liveLocation');
                    });

                    //Bid
                    Route::controller('BidController')->prefix('bid')->group(function () {
                        Route::post('create/{id}', 'create');
                        Route::get('cancel/{id}', 'cancel');
                    });

                    //message
                    Route::controller('MessageController')->prefix('ride')->group(function () {
                        Route::get('messages/{id}', 'messages');
                        Route::post('send/message/{id}', 'messageSave');
                    });

                    //driver videos
                    Route::controller('DriverVideoController')->prefix('ride')->group(function () {
                        Route::post('upload/video/{id}', 'uploadVideo');
                        Route::get('videos/{id}', 'getVideos');
                        Route::delete('video/{id}', 'deleteVideo');
                    });
                });
                //payment
                Route::controller('PaymentController')->group(function () {
                    Route::get('deposit/methods', 'methods');
                    Route::post('deposit/insert', 'depositInsert');
                });
                //ticket
                Route::controller('TicketController')->prefix('ticket')->group(function () {
                    Route::get('/', 'supportTicket');
                    Route::post('create', 'storeSupportTicket');
                    Route::get('view/{ticket}', 'viewTicket');
                    Route::post('reply/{id}', 'replyTicket');
                    Route::post('close/{id}', 'closeTicket');
                    Route::get('download/{attachment_id}', 'ticketDownload');
                });

                // Subscription
                Route::controller('SubscriptionController')->prefix('subscription')->group(function () {
                    Route::get('info', 'getSubscriptionInfo');
                    Route::post('pay', 'paySubscription');
                });
            });
        });
        Route::get('logout', 'Auth\LoginController@logout');
    });
});
