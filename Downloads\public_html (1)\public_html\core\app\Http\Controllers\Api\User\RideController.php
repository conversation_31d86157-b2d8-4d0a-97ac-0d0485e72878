<?php

namespace App\Http\Controllers\Api\User;

use App\Models\Ride;
use App\Models\Zone;
use App\Models\Coupon;
use App\Models\Driver;
use App\Models\Service;
use App\Models\SosAlert;
use App\Constants\Status;
use App\Events\NewRide;
use App\Events\Ride as EventsRide;
use Illuminate\Http\Request;
use App\Models\GatewayCurrency;
use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use App\Models\Bid;
use App\Models\Deposit;
use App\Models\Transaction;
use App\Lib\RidePaymentManager;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class RideController extends Controller
{
    public function findFareAndDistance(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_id'            => 'required|integer',
            'pickup_latitude'       => 'required|numeric',
            'pickup_longitude'      => 'required|numeric',
            'destination_latitude'  => 'required|numeric',
            'destination_longitude' => 'required|numeric',
            'waypoints'             => 'nullable|array',
            'waypoints.*.latitude'  => 'required_with:waypoints|numeric',
            'waypoints.*.longitude' => 'required_with:waypoints|numeric',
            'waypoints.*.address'   => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $service = Service::active()->find($request->service_id);

        if (!$service) {
            $notify[] = 'This service is currently unavailable';
            return apiResponse("not_found", 'error', $notify);
        }

        $zoneData = $this->getZone($request);

        if (@$zoneData['status'] == 'error') {
            $notify[] = $zoneData['message'];
            return apiResponse('not_found', 'error', $notify);
        }
        $googleMapData = $this->getGoogleMapData($request);

        if (@$googleMapData['status'] == 'error') {
            $notify[] = $googleMapData['message'];
            return apiResponse('api_error', 'error', $notify);
        }

        $pickUpZone      = $zoneData['pickup_zone'];
        $destinationZone = $zoneData['destination_zone'];
        $distance        = $googleMapData['distance'];
        $data            = $googleMapData;

        if ($pickUpZone->id == $destinationZone->id) {
            $data['min_amount']       = $service->city_min_fare * $distance;
            $data['max_amount']       = $service->city_max_fare * $distance;
            $data['recommend_amount'] = $service->city_recommend_fare * $distance;
            $data['ride_type']        = Status::CITY_RIDE;
        } else {
            $data['min_amount']       = $service->intercity_min_fare * $distance;
            $data['max_amount']       = $service->intercity_max_fare * $distance;
            $data['recommend_amount'] = $service->intercity_recommend_fare * $distance;
            $data['ride_type']        = Status::INTER_CITY_RIDE;
        }
        return apiResponse("ride_data", 'success', data: $data);
    }

    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_id'            => 'required|integer',
            'pickup_latitude'       => 'required|numeric',
            'pickup_longitude'      => 'required|numeric',
            'destination_latitude'  => 'required|numeric',
            'destination_longitude' => 'required|numeric',
            'waypoints'             => 'nullable|array',
            'waypoints.*.latitude'  => 'required_with:waypoints|numeric',
            'waypoints.*.longitude' => 'required_with:waypoints|numeric',
            'waypoints.*.address'   => 'nullable|string',
            'note'                  => 'nullable',
            'preference'            => 'nullable|string',
            'number_of_passenger'   => 'required|integer',
            'offer_amount'          => 'required|numeric',
            'payment_type'          => ['required', Rule::in(Status::PAYMENT_TYPE_GATEWAY, Status::PAYMENT_TYPE_CASH, Status::PAYMENT_TYPE_WALLET)],
            'gateway_currency_id'   => $request->payment_type == Status::PAYMENT_TYPE_GATEWAY ? 'required|exists:gateway_currencies,id' : 'nullable',
            'is_scheduled'          => 'nullable|boolean',
            'scheduled_at'          => 'required_if:is_scheduled,true|date|after:now',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $service = Service::active()->find($request->service_id);

        if (!$service) {
            $notify[] = 'This service is currently unavailable';
            return apiResponse("not_found", 'error', $notify);
        }

        $zoneData = $this->getZone($request);

        if (@$zoneData['status'] == 'error') {
            $notify[] = $zoneData['message'];
            return apiResponse('not_found', 'error', $notify);
        }

        $googleMapData = $this->getGoogleMapData($request);

        if (@$googleMapData['status'] == 'error') {
            $notify[] = $googleMapData['message'];
            return apiResponse('api_error', 'error', $notify);
        }

        $data            = $googleMapData;
        $pickUpZone      = $zoneData['pickup_zone'];
        $destinationZone = $zoneData['destination_zone'];
        $distance        = $googleMapData['distance'];
        $user            = Auth::user();

        if ($pickUpZone->country !=  $destinationZone->country) {  // can not create ride between two country
            $notify[] = "The pickup zone and destination zone must be within the same country.";
            return apiResponse('zone_error', 'error', $notify);
        }

        if ($pickUpZone->id == $destinationZone->id) {  // city ride
            $data['min_amount']            = $service->city_min_fare * $distance;
            $data['max_amount']            = $service->city_max_fare * $distance;
            $data['recommend_amount']      = $service->city_recommend_fare * $distance;
            $data['ride_type']             = Status::CITY_RIDE;
            $data['commission_percentage'] = $service->city_fare_commission;
        } else {
            $data['min_amount']            = $service->intercity_min_fare * $distance;
            $data['max_amount']            = $service->intercity_max_fare * $distance;
            $data['recommend_amount']      = $service->intercity_recommend_fare * $distance;
            $data['ride_type']             = Status::INTER_CITY_RIDE;
            $data['commission_percentage'] = $service->intercity_fare_commission;
        }

        if ($distance < gs('min_distance')) {

            $notify[] = 'Minimum distance must be ' . getAmount(gs('min_distance')) . ' km';
            return apiResponse('limit_error', 'error', $notify);
        }

        if ($request->offer_amount < $data['min_amount'] || $request->offer_amount > $data['max_amount']) {
            $notify[] = 'The offer amount must be a minimum of ' . showAmount($data['min_amount']) . ' to a maximum of ' . showAmount($data['max_amount']);
            return apiResponse('limit_error', 'error', $notify);
        }

        $ride                        = new Ride();
        $ride->uid                   = getTrx(10);
        $ride->user_id               = $user->id;
        $ride->service_id            = $request->service_id;
        $ride->pickup_location       = @$data['origin_address'];
        $ride->pickup_latitude       = $request->pickup_latitude;
        $ride->pickup_longitude      = $request->pickup_longitude;
        $ride->destination           = @$data['destination_address'];
        $ride->destination_latitude  = $request->destination_latitude;
        $ride->destination_longitude = $request->destination_longitude;
        $ride->waypoints             = isset($data['waypoints']) ? $data['waypoints'] : null;
        $ride->total_stops           = isset($data['total_stops']) ? $data['total_stops'] : 0;
        $ride->ride_type             = $data['ride_type'];
        $ride->note                  = $request->note;
        $ride->preference            = $request->preference;
        $ride->number_of_passenger   = $request->number_of_passenger;
        $ride->distance              = $distance;
        $ride->duration              = $data['duration'];
        $ride->pickup_zone_id        = $pickUpZone->id;
        $ride->destination_zone_id   = $destinationZone->id;
        $ride->recommend_amount      = $data['recommend_amount'];
        $ride->min_amount            = $data['min_amount'];
        $ride->max_amount            = $data['max_amount'];
        $ride->amount                = $request->offer_amount;
        $ride->payment_type          = $request->payment_type;
        $ride->commission_percentage = $data['commission_percentage'];
        $ride->gateway_currency_id   = $request->payment_type == Status::PAYMENT_TYPE_GATEWAY ? $request->gateway_currency_id : 0;

        // Check wallet balance if payment type is wallet
        if ($request->payment_type == Status::PAYMENT_TYPE_WALLET) {
            if ($user->balance < $request->offer_amount) {
                $notify[] = 'Insufficient balance in your wallet. Please deposit first.';
                return apiResponse('insufficient_balance', 'error', $notify);
            }
        }

        // Set shared ride fields
        $ride->is_shared = false;
        $ride->shared_ride_id = 0;

        // Set scheduled ride fields
        $ride->is_scheduled = $request->is_scheduled ? true : false;
        if ($ride->is_scheduled) {
            $ride->scheduled_at = $request->scheduled_at;
            $ride->schedule_status = 0; // Initial status (pending)
        }

        $ride->save();

        $drivers = Driver::active()
            ->where('online_status', Status::YES)
            ->where('zone_id', $ride->pickup_zone_id)
            ->where("service_id", $ride->service_id)
            ->where('dv', Status::VERIFIED)
            ->where('vv', Status::VERIFIED)
            ->notRunning()
            ->get();

        // Convert disabilities array to string if needed
        $disabilities = null;
        if ($ride->user->is_disabled && $ride->user->disabilities) {
            if (is_array($ride->user->disabilities)) {
                $disabilities = implode(', ', $ride->user->disabilities);
            } elseif (is_string($ride->user->disabilities) && $this->isValidJson($ride->user->disabilities)) {
                $disabilitiesArray = json_decode($ride->user->disabilities, true);
                $disabilities = is_array($disabilitiesArray) ? implode(', ', $disabilitiesArray) : $ride->user->disabilities;
            } else {
                $disabilities = $ride->user->disabilities;
            }
        }

        $shortCode = [
            'ride_id'         => $ride->uid,
            'service'         => $ride->service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance,
            'is_disabled'     => $ride->user->is_disabled ?? false,
            'disabilities'    => $disabilities
        ];

        $ride->load('user', 'service', 'driver', 'driver.brand');
        initializePusher();

        // Only notify drivers for immediate (non-scheduled) rides
        if (!$ride->is_scheduled) {
            foreach ($drivers as $driver) {
                notify($driver, 'NEW_RIDE', $shortCode);
                event(new NewRide("new-ride-for-driver-$driver->id", [
                    'ride'              => $ride,
                    'driver_image_path' => getFilePath('driver'),
                    'user_image_path'   => getFilePath('user'),
                ]));
            }
        } else {
            // For scheduled rides, notify the user
            notify($ride->user, 'SCHEDULED_RIDE_CREATED', [
                'ride_id'         => $ride->uid,
                'service'         => $ride->service->name,
                'pickup_location' => $ride->pickup_location,
                'destination'     => $ride->destination,
                'scheduled_time'  => showDateTime($ride->scheduled_at),
            ]);
        }

        $notify[] = $ride->is_scheduled ? 'Scheduled ride created successfully' : 'Ride created successfully';
        return apiResponse('ride_create_success', 'success', $notify, [
            'ride' => $ride
        ]);
    }

    public function details($id)
    {
        $ride = Ride::with(['bids', 'userReview', 'driver', 'service', 'driver.brand'])->where('user_id', Auth::id())->find($id);

        if (!$ride) {
            $notify[] = 'Invalid ride';
            return apiResponse('not_found', 'error', $notify);
        }

        // Include user preference if available
        $userPreference = null;
        if ($ride->preference) {
            $userPreference = $ride->preference;
        }

        $notify[] = 'Ride Details';
        return apiResponse('ride_details', 'success', $notify, [
            'ride'               => $ride,
            'waypoints'          => $ride->waypoints,
            'total_stops'        => $ride->total_stops,
            'user_preference'    => $userPreference,
            'service_image_path' => getFilePath('service'),
            'brand_image_path'   => getFilePath('brand'),
            'user_image_path'    => getFilePath('user'),
            'driver_image_path'  => getFilePath('driver'),
        ]);
    }

    public function cancel(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'cancel_reason' => 'required',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $ride = Ride::whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])->where('user_id', Auth::id())->find($id);

        if (!$ride) {
            $notify[] = 'Ride not found';
            return apiResponse("not_found", 'error', $notify);
        }

        $cancelRideCount = Ride::where('user_id', Auth::id())
            ->where('canceled_user_type', Status::USER)
            ->count();

        if ($cancelRideCount >= gs('user_cancellation_limit')) {
            $notify[] = 'You have already exceeded the cancellation limit for this month';
            return apiResponse("limit_exceeded", 'error', $notify);
        }

        $ride->cancel_reason      = $request->cancel_reason;
        $ride->canceled_user_type = Status::USER;
        $ride->status             = Status::RIDE_CANCELED;
        $ride->cancelled_at       = now();
        $ride->save();

        if ($ride->status == Status::RIDE_ACTIVE) {
            notify($ride->driver, 'CANCEL_RIDE', [
                'ride_id'         => $ride->uid,
                'reason'          => $ride->cancel_reason,
                'amount'          => showAmount($ride->amount, currencyFormat: false),
                'service'         => $ride->service->name,
                'pickup_location' => $ride->pickup_location,
                'destination'     => $ride->destination,
                'duration'        => $ride->duration,
                'distance'        => $ride->distance,
            ]);
        }

        $notify[] = 'Ride canceled successfully';
        return apiResponse("canceled_ride", 'success', $notify);
    }

    public function sos(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'latitude'  => 'required|numeric',
            'longitude' => 'required|numeric',
            'message'   => 'nullable',
        ]);

        if ($validator->fails()) {
            return apiResponse('validation_error', 'error', $validator->errors()->all());
        }

        $ride = Ride::running()->where('user_id', Auth::id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('invalid_ride', 'error', $notify);
        }

        $sosAlert            = new SosAlert();
        $sosAlert->ride_id   = $id;
        $sosAlert->latitude  = $request->latitude;
        $sosAlert->longitude = $request->longitude;
        $sosAlert->message   = $request->message;
        $sosAlert->save();

        $adminNotification            = new AdminNotification();
        $adminNotification->user_id   = $ride->user->id;
        $adminNotification->title     = 'A new SOS Alert has been created, please take action';
        $adminNotification->click_url = urlPath('admin.rides.detail', $ride->id);
        $adminNotification->save();

        $notify[] = 'SOS request successfully';
        return apiResponse("sos_request", "success", $notify);
    }

    public function quickSos(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'latitude'  => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return apiResponse('validation_error', 'error', $validator->errors()->all());
        }

        $ride = Ride::running()->where('user_id', Auth::id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('invalid_ride', 'error', $notify);
        }

        $sosAlert            = new SosAlert();
        $sosAlert->ride_id   = $id;
        $sosAlert->latitude  = $request->latitude;
        $sosAlert->longitude = $request->longitude;
        $sosAlert->message   = "Emergency Alert"; // Default emergency message
        $sosAlert->save();

        $adminNotification            = new AdminNotification();
        $adminNotification->user_id   = $ride->user->id;
        $adminNotification->title     = 'URGENT: Emergency SOS Alert Activated';
        $adminNotification->click_url = urlPath('admin.rides.detail', $ride->id);
        $adminNotification->save();

        $notify[] = 'Emergency alert sent successfully';
        return apiResponse("sos_request", "success", $notify);
    }

    public function list(Request $request)
    {
        $query = Ride::with(['driver', 'user', 'service'])
            ->filter(['ride_type', 'status'])
            ->where('user_id', Auth::id());

        // Filter by scheduled status if requested
        if ($request->has('is_scheduled')) {
            $isScheduled = filter_var($request->is_scheduled, FILTER_VALIDATE_BOOLEAN);
            $query->where('is_scheduled', $isScheduled);
        }

        $rides = $query->orderBy('id', 'desc')
            ->paginate(getPaginate());

        $notify[]      = "Get the ride list";
        $data['rides'] = $rides;
        return apiResponse("ride_list", 'success', $notify, $data);
    }

    public function scheduledRides()
    {
        $rides = Ride::with(['driver', 'user', 'service'])
            ->where('user_id', Auth::id())
            ->where('is_scheduled', true)
            ->whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])
            ->where('scheduled_at', '>', now())
            ->orderBy('scheduled_at', 'asc')
            ->paginate(getPaginate());

        $notify[]      = "Get the scheduled ride list";
        $data['rides'] = $rides;
        return apiResponse("scheduled_ride_list", 'success', $notify, $data);
    }

    private function getZone($request)
    {
        $zones           = Zone::active()->get();
        $pickupAddress   = ['lat' => $request->pickup_latitude, 'long' => $request->pickup_longitude];
        $pickupZone      = null;
        $destinationZone = null;

        foreach ($zones as $zone) {
            $pickupZone = insideZone($pickupAddress, $zone);
            if ($pickupZone) {
                $pickupZone = $zone;
                break;
            }
        }

        if (!$pickupZone) {
            return [
                'status'  => 'error',
                'message' => 'The pickup location is not inside any of our zones'
            ];
        }

        $destinationAddress = ['lat' => $request->destination_latitude, 'long' => $request->destination_longitude];

        foreach ($zones as $zone) {
            $destinationZone = insideZone($destinationAddress, $zone);

            if ($destinationZone) {
                $destinationZone = $zone;
                break;
            }
        }

        if (!$destinationZone) {
            return [
                'status'  => 'error',
                'message' => 'The destination location is not inside any of our zones'
            ];
        }

        return [
            'pickup_zone'      => $pickupZone,
            'destination_zone' => $destinationZone,
            'status'           => 'success'
        ];
    }
    private function getGoogleMapData($request)
    {
        $apiKey = gs('google_maps_api');
        $waypointsParam = '';
        $totalStops = 0;
        $waypointsData = [];

        // Process waypoints if they exist
        if (!empty($request->waypoints) && is_array($request->waypoints)) {
            $totalStops = count($request->waypoints);
            $waypointCoords = [];

            foreach ($request->waypoints as $waypoint) {
                if (isset($waypoint['latitude']) && isset($waypoint['longitude'])) {
                    $waypointCoords[] = $waypoint['latitude'] . ',' . $waypoint['longitude'];

                    // Store waypoint data for later use
                    $waypointsData[] = [
                        'latitude' => $waypoint['latitude'],
                        'longitude' => $waypoint['longitude'],
                        'address' => $waypoint['address'] ?? null
                    ];
                }
            }

            if (!empty($waypointCoords)) {
                $waypointsParam = '&waypoints=optimize:true|' . implode('|', $waypointCoords);
            }
        }

        // Use Directions API instead of Distance Matrix for more accurate results with waypoints
        $url = "https://maps.googleapis.com/maps/api/directions/json?origin={$request->pickup_latitude},{$request->pickup_longitude}&destination={$request->destination_latitude},{$request->destination_longitude}{$waypointsParam}&units=metric&key={$apiKey}";
        $response = file_get_contents($url);
        $directionsData = json_decode($response);

        if ($directionsData->status != 'OK') {
            return [
                'status'  => 'error',
                'message' => 'Something went wrong with directions API!'
            ];
        }

        // Calculate total distance and duration
        $totalDistance = 0;
        $totalDurationText = '';
        $totalDurationValue = 0;

        foreach ($directionsData->routes[0]->legs as $leg) {
            $totalDistance += $leg->distance->value;
            $totalDurationValue += $leg->duration->value;

            // If waypoint addresses weren't provided, get them from the API response
            if (!empty($waypointsData) && count($waypointsData) > 0) {
                for ($i = 0; $i < count($waypointsData); $i++) {
                    if (empty($waypointsData[$i]['address']) && isset($directionsData->routes[0]->legs[$i]->end_address)) {
                        $waypointsData[$i]['address'] = $directionsData->routes[0]->legs[$i]->end_address;
                    }
                }
            }
        }

        // Convert distance from meters to kilometers
        $distance = $totalDistance / 1000;

        // Format duration
        $hours = floor($totalDurationValue / 3600);
        $minutes = floor(($totalDurationValue % 3600) / 60);

        if ($hours > 0) {
            $totalDurationText = $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' min';
        } else {
            $totalDurationText = $minutes . ' min';
        }

        return [
            'distance'            => $distance,
            'duration'            => $totalDurationText,
            'origin_address'      => $directionsData->routes[0]->legs[0]->start_address,
            'destination_address' => $directionsData->routes[0]->legs[count($directionsData->routes[0]->legs) - 1]->end_address,
            'waypoints'           => $waypointsData,
            'total_stops'         => $totalStops,
        ];
    }

    public function bids($id)
    {
        $ride = Ride::where('user_id', Auth::id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('not_found', 'error', $notify);
        }

        $bids     = Bid::with(['driver', 'driver.service', 'driver.brand'])->where('ride_id', $ride->id)->whereIn('status', [Status::BID_PENDING, Status::BID_ACCEPTED])->get();
        $notify[] = 'All Bid';

        return apiResponse("bids", "success", $notify, [
            'bids'              => $bids,
            'ride'              => $ride,
            'driver_image_path' => getFilePath('driver'),
            'user_image_path'   => getFilePath('user'),
        ]);
    }

    public function accept($bidId)
    {
        $bid = Bid::pending()->with('ride')->whereHas('ride', function ($q) {
            return $q->pending()->where('user_id', Auth::id());
        })->find($bidId);

        if (!$bid) {
            $notify[] = 'Invalid bid';
            return apiResponse('not_found', 'error', $notify);
        }

        $bid->status      = Status::BID_ACCEPTED;
        $bid->accepted_at = now();
        $bid->save();

        //all the bid rejected after the one accept this bid
        Bid::where('id', '!=', $bid->id)->where('ride_id', $bid->ride_id)->update(['status' => Status::BID_REJECTED]);

        $ride            = $bid->ride;
        $ride->status    = Status::RIDE_ACTIVE;
        $ride->driver_id = $bid->driver_id;
        $ride->otp       = getNumber(6);
        $ride->amount    = $bid->bid_amount;
        $ride->save();

        $ride->load('driver', 'driver.brand', 'service', 'user');

        initializePusher();

        event(new NewRide("new-ride-for-driver-$ride->driver_id", ['ride' => $ride], 'bid_accept'));

        // Include disability information in the notification
        $notificationData = [
            'ride_id'         => $ride->uid,
            'amount'          => showAmount($ride->amount),
            'rider'           => $ride->user->username,
            'service'         => $ride->service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance
        ];

        // Add disability information if user is disabled
        if ($ride->user->is_disabled && $ride->user->disabilities) {
            $notificationData['is_disabled'] = true;

            // Convert disabilities array to string if needed
            $disabilities = null;
            if (is_array($ride->user->disabilities)) {
                $disabilities = implode(', ', $ride->user->disabilities);
            } elseif (is_string($ride->user->disabilities) && $this->isValidJson($ride->user->disabilities)) {
                $disabilitiesArray = json_decode($ride->user->disabilities, true);
                $disabilities = is_array($disabilitiesArray) ? implode(', ', $disabilitiesArray) : $ride->user->disabilities;
            } else {
                $disabilities = $ride->user->disabilities;
            }

            $notificationData['disabilities'] = $disabilities;
        }

        notify($ride->driver, 'ACCEPT_RIDE', $notificationData);

        $notify[] = 'Bid accepted successfully';
        return apiResponse('accepted', 'success', $notify, [
            'ride' => $ride
        ]);
    }

    public function reject($id)
    {
        $bid = Bid::pending()->with('ride')->find($id);

        if (!$bid) {
            $notify[] = 'Invalid bid';
            return apiResponse('not_found', 'error', $notify);
        }

        $ride = $bid->ride;
        if ($ride->user_id != Auth::id()) {
            $notify[] = 'This ride is not for this rider';
            return apiResponse('unauthenticated', 'error', $notify);
        }

        $bid->status = Status::BID_REJECTED;
        $bid->save();

        initializePusher();

        event(new EventsRide($ride, 'bid_reject'));

        notify($ride->user, 'BID_REJECT', [
            'ride_id'         => $ride->uid,
            'amount'          => showAmount($bid->bid_amount),
            'service'         => $ride->service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance
        ]);

        $notify[] = 'Bid rejected successfully';

        return apiResponse('rejected_bid', 'success', $notify);
    }

    public function payment($id)
    {
        $ride = Ride::where('user_id', Auth::id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('not_found', 'error', $notify);
        }

        $ride->load('driver', 'driver.brand', 'service', 'user', 'coupon');

        $gatewayCurrency = GatewayCurrency::whereHas('method', function ($gate) {
            $gate->active()->automatic();
        })->with('method')->orderby('method_code')->get();

        $user = Auth::user();
        $amount = $ride->amount - $ride->discount_amount;

        // If this is a shared ride, the user only pays half
        if ($ride->is_shared && $ride->sharedRide) {
            $amount = $amount / 2;
        }

        $hasWalletBalance = $user->balance >= $amount;

        $notify[] = "Ride Payments";
        $paymentTypes = [
            ['id' => Status::PAYMENT_TYPE_GATEWAY, 'name' => 'Gateway Payment'],
            ['id' => Status::PAYMENT_TYPE_CASH, 'name' => 'Cash Payment'],
            ['id' => Status::PAYMENT_TYPE_WALLET, 'name' => 'Wallet Payment', 'available' => $hasWalletBalance]
        ];

        return apiResponse('payment', 'success', $notify, [
            'payment_amount'     => $amount,
            'payment_types'      => $paymentTypes,
            'has_wallet_balance' => $hasWalletBalance,
            'wallet_balance'     => $user->balance,
            'gateways'           => $gatewayCurrency,
            'image_path'         => getFilePath('gateway'),
            'ride'               => $ride,
            'is_shared_ride'     => $ride->is_shared && $ride->sharedRide ? true : false,
            'original_amount'    => $ride->amount - $ride->discount_amount,
            'discount_percentage' => $ride->is_shared && $ride->sharedRide ? 50 : 0,
            'coupons'            => Coupon::orderBy('id', 'desc')->active()->get(),
            'driver_image_path'  => getFilePath('driver'),
        ]);
    }

    public function paymentSave(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'payment_type' => ['required', Rule::in(Status::PAYMENT_TYPE_GATEWAY, Status::PAYMENT_TYPE_CASH, Status::PAYMENT_TYPE_WALLET)],
            'method_code'  => 'required_if:payment_type,1',
            'currency'     => 'required_if:payment_type,1',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $ride  = Ride::where('user_id', Auth::id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('not_found', 'error', $notify);
        }

        if ($request->payment_type == Status::PAYMENT_TYPE_GATEWAY) {
            return $this->paymentViaGateway($request, $ride);
        } elseif ($request->payment_type == Status::PAYMENT_TYPE_WALLET) {
            return $this->paymentViaWallet($ride);
        } else {
            initializePusher();
            $ride->load('driver', 'user', 'service');

            // Calculate the payment amount (half for shared rides)
            $paymentAmount = $ride->amount - $ride->discount_amount;
            if ($ride->is_shared && $ride->sharedRide) {
                $paymentAmount = $paymentAmount / 2;
                event(new EventsRide($ride, 'shared-ride-cash-payment-request'));
                $notify[] = "Please give the driver " . showAmount($paymentAmount) . " in cash (50% of fare for shared ride).";
            } else {
                event(new EventsRide($ride, 'cash-payment-request'));
                $notify[] = "Please give the driver " . showAmount($paymentAmount) . " in cash.";
            }

            return apiResponse('cash_payment', 'success', $notify, [
                'ride' => $ride,
                'shared_ride' => $ride->is_shared && $ride->sharedRide ? true : false,
                'amount_to_pay' => $paymentAmount
            ]);
        }
    }

    private function paymentViaGateway($request, $ride)
    {
        // Check if this is a shared ride
        if ($ride->is_shared && $ride->sharedRide) {
            return $this->processSharedRideGatewayPayment($request, $ride);
        }

        // Regular ride payment
        $amount = $ride->amount - $ride->discount_amount;

        $gateway = GatewayCurrency::whereHas('method', function ($gateway) {
            $gateway->active()->automatic();
        })->where('method_code', $request->method_code)->where('currency', $request->currency)->first();

        if (!$gateway) {
            $notify[] = "Invalid gateway selected";
            return apiResponse('not_found', 'error', $notify);
        }

        if ($gateway->min_amount > $amount) {
            $notify[] = 'Minimum limit for this gateway is ' . showAmount($gateway->min_amount);
            return apiResponse('limit_exists', 'error', $notify);
        }
        if ($gateway->max_amount < $amount) {
            $notify[] = 'Maximum limit for this gateway is ' . showAmount($gateway->max_amount);
            return apiResponse('limit_exists', 'error', $notify);
        }

        $charge      = 0;
        $payable     = $amount + $charge;
        $finalAmount = $payable * $gateway->rate;
        $user        = Auth::user();

        $data                  = new Deposit();
        $data->from_api        = 1;
        $data->user_id         = $user->id;
        $data->method_code     = $gateway->method_code;
        $data->method_currency = strtoupper($gateway->currency);
        $data->amount          = $amount;
        $data->charge          = $charge;
        $data->rate            = $gateway->rate;
        $data->final_amount    = $finalAmount;
        $data->ride_id         = $ride->id;
        $data->btc_amount      = 0;
        $data->btc_wallet      = "";
        $data->success_url     = urlPath('user.deposit.history');
        $data->failed_url      = urlPath('user.deposit.history');
        $data->trx             = getTrx();
        $data->save();

        $notify[] = "Online Payment";

        return apiResponse("gateway_payment", "success", $notify, [
            'deposit'      => $data,
            'redirect_url' => route('deposit.app.confirm', encrypt($data->id))
        ]);
    }

    private function paymentViaWallet($ride)
    {
        $user = Auth::user();

        // Check if this is a shared ride
        if ($ride->is_shared && $ride->sharedRide) {
            return $this->processSharedRideWalletPayment($ride, $user);
        }

        // Regular ride payment
        $amount = $ride->amount - $ride->discount_amount;

        // Check if user has enough balance
        if ($user->balance < $amount) {
            $notify[] = 'Insufficient balance in your wallet. Please deposit first.';
            return apiResponse('insufficient_balance', 'error', $notify);
        }

        // Deduct amount from user's wallet
        $user->balance -= $amount;
        $user->save();

        // Create transaction record for user
        $transaction = new Transaction();
        $transaction->user_id = $user->id;
        $transaction->amount = $amount;
        $transaction->post_balance = $user->balance;
        $transaction->charge = 0;
        $transaction->trx_type = '-';
        $transaction->trx = $ride->uid;
        $transaction->remark = 'payment';
        $transaction->details = 'Ride payment ' . showAmount($amount) . ' and ride uid ' . $ride->uid . '';
        $transaction->save();

        // Process the ride payment
        (new RidePaymentManager())->payment($ride, Status::PAYMENT_TYPE_WALLET);

        // Load ride data and trigger event
        $ride->load('driver', 'driver.brand', 'service', 'user');
        initializePusher();
        event(new EventsRide($ride, 'wallet-payment-received'));

        $notify[] = 'Payment completed successfully from your wallet';
        return apiResponse('wallet_payment', 'success', $notify, [
            'ride' => $ride
        ]);
    }

    /**
     * Process wallet payment for shared rides
     */
    private function processSharedRideWalletPayment($ride, $user)
    {
        // We're processing a shared ride payment
        // The shared ride details are available in $ride->sharedRide if needed

        // Calculate half of the fare
        $fullAmount = $ride->amount - $ride->discount_amount;
        $halfAmount = $fullAmount / 2;

        // Check if user has enough balance
        if ($user->balance < $halfAmount) {
            $notify[] = 'Insufficient balance in your wallet. Please deposit first.';
            return apiResponse('insufficient_balance', 'error', $notify);
        }

        // Deduct half amount from user's wallet
        $user->balance -= $halfAmount;
        $user->save();

        // Create transaction record
        $transaction = new Transaction();
        $transaction->user_id = $user->id;
        $transaction->amount = $halfAmount;
        $transaction->post_balance = $user->balance;
        $transaction->charge = 0;
        $transaction->trx_type = '-';
        $transaction->trx = $ride->uid;
        $transaction->remark = 'shared_ride_payment';
        $transaction->details = 'Shared ride payment (50% of fare) ' . showAmount($halfAmount) . ' and ride uid ' . $ride->uid;
        $transaction->save();

        // Process the ride payment
        (new RidePaymentManager())->payment($ride, Status::PAYMENT_TYPE_WALLET, $halfAmount);

        // Load ride data and trigger event
        $ride->load('driver', 'driver.brand', 'service', 'user');
        initializePusher();
        event(new EventsRide($ride, 'shared-ride-wallet-payment-received'));

        $notify[] = 'Payment completed successfully from your wallet (50% of fare for shared ride)';
        return apiResponse('wallet_payment', 'success', $notify, [
            'ride' => $ride,
            'shared_ride' => true,
            'amount_paid' => $halfAmount
        ]);
    }

    /**
     * Process gateway payment for shared rides
     */
    private function processSharedRideGatewayPayment($request, $ride)
    {
        // Calculate half of the fare for shared rides
        $fullAmount = $ride->amount - $ride->discount_amount;
        $halfAmount = $fullAmount / 2;

        $gateway = GatewayCurrency::whereHas('method', function ($gateway) {
            $gateway->active()->automatic();
        })->where('method_code', $request->method_code)->where('currency', $request->currency)->first();

        if (!$gateway) {
            $notify[] = "Invalid gateway selected";
            return apiResponse('not_found', 'error', $notify);
        }

        if ($gateway->min_amount > $halfAmount) {
            $notify[] = 'Minimum limit for this gateway is ' . showAmount($gateway->min_amount);
            return apiResponse('limit_exists', 'error', $notify);
        }
        if ($gateway->max_amount < $halfAmount) {
            $notify[] = 'Maximum limit for this gateway is ' . showAmount($gateway->max_amount);
            return apiResponse('limit_exists', 'error', $notify);
        }

        $charge      = 0;
        $payable     = $halfAmount + $charge;
        $finalAmount = $payable * $gateway->rate;
        $user        = Auth::user();

        // Create a deposit record for the half amount
        $data                  = new Deposit();
        $data->from_api        = 1;
        $data->user_id         = $user->id;
        $data->method_code     = $gateway->method_code;
        $data->method_currency = strtoupper($gateway->currency);
        $data->amount          = $halfAmount; // Only pay half for shared ride
        $data->charge          = $charge;
        $data->rate            = $gateway->rate;
        $data->final_amount    = $finalAmount;
        $data->ride_id         = $ride->id;
        $data->btc_amount      = 0;
        $data->btc_wallet      = "";
        $data->success_url     = urlPath('user.deposit.history');
        $data->failed_url      = urlPath('user.deposit.history');
        $data->trx             = getTrx();
        $data->is_shared_ride  = 1; // Mark as shared ride payment
        $data->save();

        $notify[] = "Online Payment (50% of fare for shared ride)";

        return apiResponse("gateway_payment", "success", $notify, [
            'deposit'      => $data,
            'redirect_url' => route('deposit.app.confirm', encrypt($data->id)),
            'shared_ride'  => true,
            'amount_paid'  => $halfAmount
        ]);
    }

    /**
     * Check if a string is valid JSON
     *
     * @param string $string
     * @return bool
     */
    private function isValidJson($string) {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}
